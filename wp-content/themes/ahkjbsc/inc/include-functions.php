<?php
/**
 * This file will include all available function files.
 *
 * @package ahkjbsc-old
 */

function get_theme_css($path){
    return get_stylesheet_directory_uri() . '/assets/css/'. $path;
}
function get_theme_img($path){
    return get_stylesheet_directory_uri() . '/assets/img/'. $path;
}
function get_theme_js($path){
    return get_stylesheet_directory_uri() . '/assets/js/'. $path;
}
function theme_css($path){
    echo get_theme_css($path);
}
function theme_img($path){
    echo get_theme_img($path);
}
function theme_js($path){
    echo get_theme_js($path);
}

register_nav_menus([
    'top' => '头部菜单',
    'menu' => '主菜单',
    'footer' => '页脚菜单',
]);

// 添加主题支持
add_theme_support('post-thumbnails');
add_theme_support('automatic-feed-links');
add_theme_support('title-tag');
add_theme_support('html5', array(
    'search-form',
    'comment-form',
    'comment-list',
    'gallery',
    'caption',
));

// 设置特色图片尺寸
set_post_thumbnail_size(800, 600, true);
add_image_size('article-thumb', 300, 200, true);
add_image_size('sidebar-thumb', 150, 100, true);


//去除admin bar左侧的wordpress logo和链接
add_action('admin_bar_menu', 'remove_logo', 25);
function remove_logo($wp_admin_bar)
{
    $wp_admin_bar->remove_node('wp-logo');
}
//WP 前台顶部清理 尚未验证
function cwp_header_clean_up()
{
    if (!is_admin()) {
        foreach (array('wp_generator', 'rsd_link', 'index_rel_link', 'start_post_rel_link', 'wlwmanifest_link') as $clean) {remove_action('wp_head', $clean);}
        remove_action('wp_head', 'feed_links_extra', 3);
        remove_action('wp_head', 'feed_links', 2);
        remove_action('wp_head', 'parent_post_rel_link', 10, 0);
        remove_action('wp_head', 'start_post_rel_link', 10, 0);
        remove_action('wp_head', 'adjacent_posts_rel_link', 10, 0);
        foreach (array('single_post_title', 'bloginfo', 'wp_title', 'category_description', 'list_cats', 'comment_author', 'comment_text', 'the_title', 'the_content', 'the_excerpt') as $where) {
            remove_filter($where, 'wptexturize');
        }
        wp_deregister_script('l10n');
    }
}
//移除仪表盘里的下工具
function cwp_remove_dashboard_widgets()
{
    global $wp_meta_boxes;
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_quick_press']);//快速草稿
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_incoming_links']); //不知道是什么
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_right_now']);//概览
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_plugins']); //不知道是什么
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_drafts']); //不知道是什么
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_comments']); //不知道是什么
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_primary']); //wordpress新闻
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_secondary']);
}
add_action('wp_dashboard_setup', 'cwp_remove_dashboard_widgets', 11);
//子主题的css库,所有的库都在这里
function my_theme_enqueue_style()
{
    wp_enqueue_style('ant', get_theme_css("ant.css"));
    wp_enqueue_style('news-list', get_theme_css("news-list.css")); // 新闻列表样式
    wp_enqueue_style('article-detail', get_theme_css("article-detail.css")); // 文章详情样式
    wp_enqueue_style('comments', get_theme_css("comments.css")); // 评论样式
    wp_enqueue_style('print', get_theme_css("print.css"), array(), null, 'print'); // 打印样式

    wp_enqueue_style('style', get_stylesheet_directory_uri() . '/style.css'); //自主题的css

}
add_action('wp_enqueue_scripts', 'my_theme_enqueue_style');
//加载自己的jquery库
function my_module_script()
{
    wp_enqueue_script('slider', get_theme_js("jquery.SuperSlide.2.1.3.js"), array('jquery'), null);
    wp_enqueue_script('menu', get_theme_js("menu.js"), array('jquery'), null);
}
add_action('wp_footer', 'my_module_script');

// 文章浏览量统计
function track_post_views($post_id) {
    if (!is_single()) return;
    if (empty($post_id)) {
        global $post;
        $post_id = $post->ID;
    }

    $views = get_post_meta($post_id, 'post_views', true);
    $views = $views ? $views : 0;
    $views++;

    update_post_meta($post_id, 'post_views', $views);
}
add_action('wp_head', 'track_post_views');

// 获取文章浏览量
function get_post_views($post_id = null) {
    if (!$post_id) {
        global $post;
        $post_id = $post->ID;
    }

    $views = get_post_meta($post_id, 'post_views', true);
    return $views ? $views : 0;
}

//将自带的jquery移动到脚部，并在bootstarp最前面
function mytheme_move_jquery_to_footer()
{
    wp_scripts()->add_data('jquery', 'group', 1);
    wp_scripts()->add_data('jquery-core', 'group', 1);
    wp_scripts()->add_data('jquery-migrate', 'group', 1);
}
add_action('wp_enqueue_scripts', 'mytheme_move_jquery_to_footer');

add_filter('admin_title', 'remove_login_title', 10, 2);
function remove_login_title()
{
    return get_bloginfo('name');
}
// 移除管理界面的“帮助选项卡”
add_action('in_admin_header', function () {
    global $current_screen;
    $current_screen->remove_help_tabs();
});
//移除概览中的旻亮主题链接
function update_right_now_message_example($content)
{
    $theme_name = wp_get_theme();
    $content    = sprintf('%1$s', $theme_name);

    return $content;
}
add_filter('update_right_now_text', 'update_right_now_message_example');
//设置区块的动作文字提示
add_action('admin_print_footer_scripts', function () {
    // Make sure the `wp-i18n` has been "done".
    if (wp_script_is('wp-i18n')):
        ?>
        <script>
            wp.i18n.setLocaleData({
                'Additional CSS class(es)': ['添加的动作,可直接复制粘贴动作码在表格中,如：希望从左边飘入，则wow slideInLeft'],
                'Separate multiple classes with spaces.':['动作及对应动作码：从左边飘入---wow slideInLeft；从右边飘入--wow slideInRight；从上方飘入--wow bounceInUp；从下方进入--wow slideInDown']
            });
        </script>
    <?php
    endif;
}, 11);

//注销不需要的blocks 看wp-includes/blocks的json文件
add_filter( 'allowed_block_types_all', 'misha_allowed_block_types');
function misha_allowed_block_types($allowed_blocks)
{
    $allowed_blocks = array(
        'core/paragraph', //段落--------文字
        'core/heading', //标题
        'core/list', //列表
        'core/quote', //引用
        'core/code', //代码
        'core/freeform', //经典编辑器
        'core/preformatted', //预格式
        'core/pullquote', //引文
        'core/table', //表格
        'core/verse', //诗文
        'core/image', //图片-------媒体
        'core/gallery', //画廊
        'core/audio', //音频
        'core/cover', //封面
        'core/file', //文件
        'core/media-text', //媒体和文本
        'core/video', //视频
        'core/buttons', //多个按钮-------设计
        'core/columns', //栏目
        'core/group', //组
        'core/more', //更多
        'core/nextpage', //分页符
        'core/separator', //分隔符
        'core/spacer', //空格
        'core/site-logo', //站点logo
        'core/site-tagline', //站点副标题
        'core/site-title', //站点标题
        'core/query-title', //归档标题
        'core/post-terms', //文章分类,文章标签
        'core/shortcode', //简码-----小工具
        'core/archives', //归档
        'core/calendar', //日历
        'core/categories', //分类
        'core/html', //自定义HTML
        'core/latest-comments', //最新评论
        'core/latest-posts', //最新文章
        'core/page-list', //页面列表
        'core/rss', //RSS
        'core/social-links', //社交图标
        'core/tag-cloud', //标签云
        'core/search', //搜索
        'core/query', //循环查询，文章列表--------主题
        'core/post-title', //文章标题
        'core/post-content', //文章内容
        'core/post-date', //文章日期
        'core/post-excerpt', //文章摘要
        'core/post-featured-image', //文章特色图片
        'core/loginout', //登陆/注销
        'core/post-template', //有问题不知道是什么
    );
    return $allowed_blocks;
}

function remove_default_blocks($allowed_blocks)
{
    // Get widget blocks and registered by plugins blocks
    $registered_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();

    // Disable Widgets Blocks
    unset($registered_blocks['core/embed']);

    // Now $registered_blocks contains only blocks registered by plugins, but we need keys only
    $registered_blocks = array_keys($registered_blocks);

    // Merge allowed core blocks with plugins blocks
    return array_merge(array(), $registered_blocks);
}
add_filter('allowed_block_types_all', 'remove_default_blocks');

