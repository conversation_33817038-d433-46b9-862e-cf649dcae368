<?php
/**
 * Archive template for cooperation post type
 *
 * @package ahkjbsc
 */

get_header();
?>

<div class="cooperation-archive">
    <div class="container">
        <!-- 筛选器区域 -->
        <div class="cooperation-filters">
            <!-- 机构类型 -->
            <div class="filter-group">
                <div class="filter-label">机构类型</div>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="organ_type" data-value="">全部</button>
                    <?php
                    $organ_types = get_terms(array(
                        'taxonomy' => 'organ_type',
                        'hide_empty' => true,
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ));

                    if ($organ_types && !is_wp_error($organ_types)) :
                        foreach ($organ_types as $type) :
                    ?>
                    <button class="filter-btn" data-filter="organ_type" data-value="<?php echo esc_attr($type->slug); ?>">
                        <?php echo esc_html($type->name); ?>
                        <span class="count">(<?php echo $type->count; ?>)</span>
                    </button>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </div>
            </div>

            <!-- 成立时间 -->
            <div class="filter-group">
                <div class="filter-label">成立时间</div>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="create_date" data-value="">全部</button>
                    <button class="filter-btn" data-filter="create_date" data-value="2020-">2020年以后</button>
                    <button class="filter-btn" data-filter="create_date" data-value="2015-2019">2015-2019年</button>
                    <button class="filter-btn" data-filter="create_date" data-value="2010-2014">2010-2014年</button>
                    <button class="filter-btn" data-filter="create_date" data-value="2005-2009">2005-2009年</button>
                    <button class="filter-btn" data-filter="create_date" data-value="-2004">2004年以前</button>
                </div>
            </div>

            <!-- 入驻时间 -->
            <div class="filter-group">
                <div class="filter-label">入驻时间</div>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="enter_date" data-value="">全部</button>
                    <button class="filter-btn" data-filter="enter_date" data-value="recent_year">近一年</button>
                    <button class="filter-btn" data-filter="enter_date" data-value="recent_half_year">近半年</button>
                    <button class="filter-btn" data-filter="enter_date" data-value="recent_quarter">近三个月</button>
                    <button class="filter-btn" data-filter="enter_date" data-value="recent_month">近一个月</button>
                </div>
            </div>

            <!-- 企业人数 -->
            <div class="filter-group">
                <div class="filter-label">企业人数</div>
                <div class="filter-options">
                    <button class="filter-btn active" data-filter="person" data-value="">全部</button>
                    <button class="filter-btn" data-filter="person" data-value="1-10">1-10人</button>
                    <button class="filter-btn" data-filter="person" data-value="11-50">11-50人</button>
                    <button class="filter-btn" data-filter="person" data-value="51-100">51-100人</button>
                    <button class="filter-btn" data-filter="person" data-value="101-500">101-500人</button>
                    <button class="filter-btn" data-filter="person" data-value="500+">500人以上</button>
                </div>
            </div>
        </div>

        <!-- 搜索和排序 -->
        <div class="cooperation-controls">
            <div class="search-section">
                <div class="ant-input-group ant-input-search">
                    <input type="text" id="cooperation-search" class="ant-input" placeholder="搜索机构名称、关键词..." />
                    <span class="ant-input-group-addon">
                        <button type="button" class="ant-btn ant-btn-primary" onclick="searchCooperation()">
                            <i class="anticon anticon-search"></i>
                        </button>
                    </span>
                </div>
            </div>

            <div class="sort-section">
                <select id="cooperation-sort" class="ant-select" onchange="sortCooperation()">
                    <option value="date_desc">最新发布</option>
                    <option value="date_asc">最早发布</option>
                    <option value="enter_date_desc">最新入驻</option>
                    <option value="enter_date_asc">最早入驻</option>
                    <option value="person_desc">人数最多</option>
                    <option value="person_asc">人数最少</option>
                </select>
            </div>

            <div class="results-count">
                <span id="results-total">共找到 <strong><?php echo wp_count_posts('cooperation')->publish; ?></strong> 家合作机构</span>
            </div>
        </div>

        <!-- 机构列表 -->
        <div class="cooperation-list" id="cooperation-list">
            <?php
            // 获取筛选参数
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

            // 构建查询参数
            $args = array(
                'post_type' => 'cooperation',
                'posts_per_page' => 12,
                'paged' => $paged,
                'post_status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC'
            );

            // 添加机构类型筛选
            if (isset($_GET['organ_type']) && !empty($_GET['organ_type'])) {
                $args['tax_query'] = array(
                    array(
                        'taxonomy' => 'organ_type',
                        'field'    => 'slug',
                        'terms'    => sanitize_text_field($_GET['organ_type']),
                    ),
                );
            }

            // 添加自定义字段筛选
            $meta_query = array('relation' => 'AND');

            // 企业人数筛选
            if (isset($_GET['person']) && !empty($_GET['person'])) {
                $person_range = sanitize_text_field($_GET['person']);
                switch ($person_range) {
                    case '1-10':
                        $meta_query[] = array(
                            'key' => 'person',
                            'value' => array(1, 10),
                            'type' => 'NUMERIC',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '11-50':
                        $meta_query[] = array(
                            'key' => 'person',
                            'value' => array(11, 50),
                            'type' => 'NUMERIC',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '51-100':
                        $meta_query[] = array(
                            'key' => 'person',
                            'value' => array(51, 100),
                            'type' => 'NUMERIC',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '101-500':
                        $meta_query[] = array(
                            'key' => 'person',
                            'value' => array(101, 500),
                            'type' => 'NUMERIC',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '500+':
                        $meta_query[] = array(
                            'key' => 'person',
                            'value' => 500,
                            'type' => 'NUMERIC',
                            'compare' => '>'
                        );
                        break;
                }
            }

            // 成立时间筛选
            if (isset($_GET['create_date']) && !empty($_GET['create_date'])) {
                $create_date_range = sanitize_text_field($_GET['create_date']);
                switch ($create_date_range) {
                    case '2020-':
                        $meta_query[] = array(
                            'key' => 'create_date',
                            'value' => '2020-01-01',
                            'type' => 'DATE',
                            'compare' => '>='
                        );
                        break;
                    case '2015-2019':
                        $meta_query[] = array(
                            'key' => 'create_date',
                            'value' => array('2015-01-01', '2019-12-31'),
                            'type' => 'DATE',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '2010-2014':
                        $meta_query[] = array(
                            'key' => 'create_date',
                            'value' => array('2010-01-01', '2014-12-31'),
                            'type' => 'DATE',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '2005-2009':
                        $meta_query[] = array(
                            'key' => 'create_date',
                            'value' => array('2005-01-01', '2009-12-31'),
                            'type' => 'DATE',
                            'compare' => 'BETWEEN'
                        );
                        break;
                    case '-2004':
                        $meta_query[] = array(
                            'key' => 'create_date',
                            'value' => '2004-12-31',
                            'type' => 'DATE',
                            'compare' => '<='
                        );
                        break;
                }
            }

            // 入驻时间筛选
            if (isset($_GET['enter_date']) && !empty($_GET['enter_date'])) {
                $enter_date_range = sanitize_text_field($_GET['enter_date']);
                $current_date = current_time('Y-m-d');

                switch ($enter_date_range) {
                    case 'recent_year':
                        $date_from = date('Y-m-d', strtotime('-1 year'));
                        $meta_query[] = array(
                            'key' => 'enter_date',
                            'value' => $date_from,
                            'type' => 'DATE',
                            'compare' => '>='
                        );
                        break;
                    case 'recent_half_year':
                        $date_from = date('Y-m-d', strtotime('-6 months'));
                        $meta_query[] = array(
                            'key' => 'enter_date',
                            'value' => $date_from,
                            'type' => 'DATE',
                            'compare' => '>='
                        );
                        break;
                    case 'recent_quarter':
                        $date_from = date('Y-m-d', strtotime('-3 months'));
                        $meta_query[] = array(
                            'key' => 'enter_date',
                            'value' => $date_from,
                            'type' => 'DATE',
                            'compare' => '>='
                        );
                        break;
                    case 'recent_month':
                        $date_from = date('Y-m-d', strtotime('-1 month'));
                        $meta_query[] = array(
                            'key' => 'enter_date',
                            'value' => $date_from,
                            'type' => 'DATE',
                            'compare' => '>='
                        );
                        break;
                }
            }

            if (count($meta_query) > 1) {
                $args['meta_query'] = $meta_query;
            }

            // 添加搜索功能
            if (isset($_GET['s']) && !empty($_GET['s'])) {
                $args['s'] = sanitize_text_field($_GET['s']);
            }

            // 执行查询
            $cooperation_query = new WP_Query($args);

            if ($cooperation_query->have_posts()) :
            ?>
            <div class="cooperation-grid">
                <?php while ($cooperation_query->have_posts()) : $cooperation_query->the_post(); ?>
                <div class="cooperation-card">
                    <div class="card-header">
                        <?php if (has_post_thumbnail()) : ?>
                        <div class="card-image">
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium', array('class' => 'cooperation-logo')); ?>
                            </a>
                        </div>
                        <?php endif; ?>

                        <div class="card-info">
                            <h3 class="cooperation-name">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>

                            <?php
                            $organ_types = get_the_terms(get_the_ID(), 'organ_type');
                            if ($organ_types && !is_wp_error($organ_types)) :
                            ?>
                            <div class="cooperation-type">
                                <?php foreach ($organ_types as $type) : ?>
                                <span class="type-tag"><?php echo esc_html($type->name); ?></span>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="cooperation-excerpt">
                            <?php
                            if (has_excerpt()) {
                                echo wp_trim_words(get_the_excerpt(), 100, '...');
                            } else {
                                echo wp_trim_words(get_the_content(), 100, '...');
                            }
                            ?>
                        </div>

                        <div class="cooperation-meta">
                            <?php
                            $create_date = get_field('create_date');
                            $enter_date = get_field('enter_date');
                            $person = get_field('person');
                            ?>

                            <?php if ($create_date) : ?>
                            <div class="meta-item">
                                <i class="anticon anticon-calendar"></i>
                                <span class="meta-label">成立时间：</span>
                                <span class="meta-value"><?php echo date('Y年', strtotime($create_date)); ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if ($enter_date) : ?>
                            <div class="meta-item">
                                <i class="anticon anticon-home"></i>
                                <span class="meta-label">入驻时间：</span>
                                <span class="meta-value"><?php echo date('Y-m-d', strtotime($enter_date)); ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if ($person) : ?>
                            <div class="meta-item">
                                <i class="anticon anticon-team"></i>
                                <span class="meta-label">企业人数：</span>
                                <span class="meta-value"><?php echo $person; ?>人</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card-footer">
                        <a href="<?php the_permalink(); ?>" class="ant-btn ant-btn-primary">
                            查看详情
                            <i class="anticon anticon-right"></i>
                        </a>

                        <div class="card-actions">
                            <button class="action-btn" title="收藏">
                                <i class="anticon anticon-heart"></i>
                            </button>
                            <button class="action-btn" title="分享">
                                <i class="anticon anticon-share-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            </div>

            <!-- 分页 -->
            <?php if ($cooperation_query->max_num_pages > 1) : ?>
            <div class="cooperation-pagination">
                <div class="ant-pagination">
                    <?php
                    $pagination_args = array(
                        'total' => $cooperation_query->max_num_pages,
                        'current' => $paged,
                        'format' => '?paged=%#%',
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'prev_next' => true,
                        'prev_text' => '<i class="anticon anticon-left"></i> 上一页',
                        'next_text' => '下一页 <i class="anticon anticon-right"></i>',
                        'type' => 'array'
                    );

                    $pagination_links = paginate_links($pagination_args);

                    if ($pagination_links) {
                        foreach ($pagination_links as $link) {
                            $link = str_replace('page-numbers', 'ant-pagination-item', $link);
                            $link = str_replace('current', 'ant-pagination-item-active', $link);
                            echo $link;
                        }
                    }
                    ?>
                </div>
            </div>
            <?php endif; ?>

            <?php else : ?>
            <div class="no-results">
                <div class="ant-empty">
                    <div class="ant-empty-image">
                        <svg width="64" height="41" viewBox="0 0 64 41">
                            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                                <ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7"></ellipse>
                                <g fill-rule="nonzero" stroke="#D9D9D9">
                                    <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.258L9 12.76V22h46v-9.24z"></path>
                                    <path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" fill="#FAFAFA"></path>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <p class="ant-empty-description">暂无符合条件的合作机构</p>
                    <div class="ant-empty-footer">
                        <button class="ant-btn ant-btn-primary" onclick="clearFilters()">
                            清除筛选条件
                        </button>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php wp_reset_postdata(); ?>
        </div>
    </div>
</div>

<!-- JavaScript 功能 -->
<script>
// 筛选功能
document.addEventListener('DOMContentLoaded', function() {
    // 筛选按钮点击事件
    const filterBtns = document.querySelectorAll('.filter-btn:not(.more-btn)');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filterType = this.dataset.filter;
            const filterValue = this.dataset.value;

            // 更新按钮状态
            const groupBtns = this.parentElement.querySelectorAll('.filter-btn:not(.more-btn)');
            groupBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 执行筛选
            applyFilters();
        });
    });

    // 更多按钮功能
    const moreBtns = document.querySelectorAll('.more-btn');
    moreBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const toggleTarget = this.dataset.toggle;
            const extendedOptions = document.getElementById(toggleTarget + '-extended');

            if (extendedOptions) {
                if (extendedOptions.style.display === 'none') {
                    extendedOptions.style.display = 'flex';
                    this.innerHTML = '收起 ▲';
                } else {
                    extendedOptions.style.display = 'none';
                    this.innerHTML = '更多 ▼';
                }
            }
        });
    });
});

// 应用筛选
function applyFilters() {
    const activeFilters = {};

    // 收集所有激活的筛选条件
    document.querySelectorAll('.filter-btn.active').forEach(btn => {
        const filterType = btn.dataset.filter;
        const filterValue = btn.dataset.value;

        if (filterValue && filterType) {
            activeFilters[filterType] = filterValue;
        }
    });

    // 构建URL参数
    const urlParams = new URLSearchParams();
    Object.keys(activeFilters).forEach(key => {
        urlParams.set(key, activeFilters[key]);
    });

    // 添加搜索关键词
    const searchInput = document.getElementById('cooperation-search');
    if (searchInput && searchInput.value.trim()) {
        urlParams.set('s', searchInput.value.trim());
    }

    // 添加排序
    const sortSelect = document.getElementById('cooperation-sort');
    if (sortSelect && sortSelect.value) {
        urlParams.set('sort', sortSelect.value);
    }

    // 跳转到新URL
    const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
    // window.location.href = newUrl;
}

// 初始化页面状态
function initPageState() {
    const urlParams = new URLSearchParams(window.location.search);

    // 恢复筛选状态
    urlParams.forEach((value, key) => {
        if (key === 's') {
            // 恢复搜索关键词
            const searchInput = document.getElementById('cooperation-search');
            if (searchInput) {
                searchInput.value = value;
            }
        } else if (key === 'sort') {
            // 恢复排序选项
            const sortSelect = document.getElementById('cooperation-sort');
            if (sortSelect) {
                sortSelect.value = value;
            }
        } else {
            // 恢复筛选按钮状态
            const filterBtn = document.querySelector(`[data-filter="${key}"][data-value="${value}"]`);
            if (filterBtn) {
                // 移除同组其他按钮的激活状态
                const groupBtns = filterBtn.parentElement.querySelectorAll('.filter-btn:not(.more-btn)');
                groupBtns.forEach(btn => btn.classList.remove('active'));

                // 激活当前按钮
                filterBtn.classList.add('active');
            }
        }
    });
}

// 页面加载时初始化状态
document.addEventListener('DOMContentLoaded', function() {
    initPageState();
});

// 搜索功能
function searchCooperation() {
    applyFilters();
}

// 排序功能
function sortCooperation() {
    applyFilters();
}

// 清除筛选条件
function clearFilters() {
    window.location.href = window.location.pathname;
}

// 回车键搜索
document.getElementById('cooperation-search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchCooperation();
    }
});

// 收藏功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.action-btn[title="收藏"]')) {
        e.preventDefault();
        const btn = e.target.closest('.action-btn');
        const icon = btn.querySelector('i');

        if (icon.classList.contains('anticon-heart')) {
            icon.classList.remove('anticon-heart');
            icon.classList.add('anticon-heart', 'favorited');
            btn.style.color = '#ff4d4f';
        } else {
            icon.classList.remove('favorited');
            btn.style.color = '';
        }
    }
});

// 分享功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.action-btn[title="分享"]')) {
        e.preventDefault();
        const card = e.target.closest('.cooperation-card');
        const title = card.querySelector('.cooperation-name a').textContent;
        const url = card.querySelector('.cooperation-name a').href;

        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(url).then(() => {
                alert('链接已复制到剪贴板');
            });
        }
    }
});
</script>

<?php
get_footer();
?>
