<?php
/**
 * 404 Error Page Template
 *
 * @package ahkjbsc
 */

get_header();

dd($wp_rewrite);
?>

<div class="page-404">
    <!-- 404 主内容区域 -->
    <div class="error-404-wrapper">
        <div class="container">
            <div class="error-404-content">
                <!-- 404 图标和文字 -->
                <div class="error-404-main">
                    <div class="error-404-image">
                        <div class="error-number">404</div>
                        <div class="error-illustration">
                            <svg width="252" height="294" viewBox="0 0 252 294" fill="none">
                                <defs>
                                    <linearGradient id="paint0_linear" x1="126" y1="0" x2="126" y2="294" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#1890ff" stop-opacity="0.8"/>
                                        <stop offset="1" stop-color="#1890ff" stop-opacity="0.1"/>
                                    </linearGradient>
                                </defs>
                                <path d="M126 294C195.036 294 252 228.036 252 147C252 65.9644 195.036 0 126 0C56.9644 0 0 65.9644 0 147C0 228.036 56.9644 294 126 294Z" fill="url(#paint0_linear)"/>
                                <circle cx="126" cy="147" r="100" fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
                                <path d="M96 127C96 116.507 103.507 108 114 108C124.493 108 132 116.507 132 127" stroke="#1890ff" stroke-width="4" stroke-linecap="round"/>
                                <path d="M120 167C120 156.507 127.507 148 138 148C148.493 148 156 156.507 156 167" stroke="#1890ff" stroke-width="4" stroke-linecap="round"/>
                                <circle cx="105" cy="135" r="8" fill="#1890ff"/>
                                <circle cx="147" cy="175" r="8" fill="#1890ff"/>
                                <path d="M90 190C100 200 120 205 126 205C132 205 152 200 162 190" stroke="#ff4d4f" stroke-width="3" stroke-linecap="round"/>
                            </svg>
                        </div>
                    </div>

                    <div class="error-404-text">
                        <h1 class="error-title">页面不存在</h1>
                        <p class="error-description">
                            抱歉，您访问的页面不存在或已被移除。<br>
                            请检查URL是否正确，或者尝试以下方式找到您需要的内容。
                        </p>

                        <!-- 快速操作按钮 -->
                        <div class="error-actions">
                            <a href="<?php echo home_url(); ?>" class="ant-btn ant-btn-primary ant-btn-lg">
                                <i class="anticon anticon-home"></i>
                                返回首页
                            </a>
                            <button onclick="history.back()" class="ant-btn ant-btn-default ant-btn-lg">
                                <i class="anticon anticon-arrow-left"></i>
                                返回上一页
                            </button>
                            <button onclick="location.reload()" class="ant-btn ant-btn-default ant-btn-lg">
                                <i class="anticon anticon-reload"></i>
                                刷新页面
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助内容区域 -->
    <div class="error-404-help">
        <div class="container">
            <div class="ant-row">
                <!-- 搜索功能 -->
                <div class="ant-col ant-col-24">
                    <div class="help-section search-section">
                        <h3 class="help-title">
                            <span role="img" aria-label="search" class="anticon anticon-search"
                                              style="font-size: 16px;"><svg
                                                    viewBox="64 64 896 896" focusable="false" data-icon="search"
                                                    width="1em" height="1em" fill="currentColor" aria-hidden="true"><path
                                                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg></span>
                            搜索您需要的内容
                        </h3>
                        <div class="search-form-wrapper">
                            <form role="search" method="get" action="<?php echo home_url('/'); ?>" class="search-form">
                                <div class="ant-input-group ant-input-search ant-input-search-large">
                                    <input type="search" name="s" class="ant-input" placeholder="请输入关键词搜索..." value="<?php echo get_search_query(); ?>" />
                                    <span class="ant-input-group-addon">
                                        <button type="submit" class="ant-btn ant-btn-primary ant-btn-icon-only">
                                            <span role="img" aria-label="search" class="anticon anticon-search"
                                              style="font-size: 16px;"><svg
                                                    viewBox="64 64 896 896" focusable="false" data-icon="search"
                                                    width="1em" height="1em" fill="currentColor" aria-hidden="true"><path
                                                        d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg></span>
                                        </button>
                                    </span>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ant-row" style="margin-top: 40px;">
                <!-- 热门分类 -->
                <div class="ant-col ant-col-8">
                    <div class="help-section">
                        <h4 class="help-section-title">
                            <i class="anticon anticon-folder"></i>
                            热门分类
                        </h4>
                        <div class="categories-list">
                            <?php
                            $categories = get_categories(array(
                                'orderby' => 'count',
                                'order' => 'DESC',
                                'number' => 8,
                                'hide_empty' => true
                            ));

                            if ($categories) :
                                foreach ($categories as $category) :
                            ?>
                            <div class="category-item">
                                <a href="<?php echo get_category_link($category->term_id); ?>" class="category-link">
                                    <span class="category-name"><?php echo esc_html($category->name); ?></span>
                                    <span class="category-count">(<?php echo $category->count; ?>)</span>
                                </a>
                            </div>
                            <?php
                                endforeach;
                            else :
                            ?>
                            <p class="no-categories">暂无分类</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 最新文章 -->
                <div class="ant-col ant-col-8">
                    <div class="help-section">
                        <h4 class="help-section-title">
                            <i class="anticon anticon-clock-circle"></i>
                            最新文章
                        </h4>
                        <div class="recent-posts-list">
                            <?php
                            $recent_posts = new WP_Query(array(
                                'post_type' => 'post',
                                'posts_per_page' => 6,
                                'post_status' => 'publish',
                                'orderby' => 'date',
                                'order' => 'DESC'
                            ));

                            if ($recent_posts->have_posts()) :
                                while ($recent_posts->have_posts()) : $recent_posts->the_post();
                            ?>
                            <div class="recent-post-item">
                                <a href="<?php the_permalink(); ?>" class="recent-post-link">
                                    <div class="recent-post-title"><?php the_title(); ?></div>
                                    <div class="recent-post-date"><?php echo get_the_date('m-d'); ?></div>
                                </a>
                            </div>
                            <?php
                                endwhile;
                                wp_reset_postdata();
                            else :
                            ?>
                            <p class="no-posts">暂无文章</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 常用链接 -->
                <div class="ant-col ant-col-8">
                    <div class="help-section">
                        <h4 class="help-section-title">
                            <i class="anticon anticon-link"></i>
                            常用链接
                        </h4>
                        <div class="useful-links">
                            <div class="link-item">
                                <a href="<?php echo home_url(); ?>" class="useful-link">
                                    <i class="anticon anticon-home"></i>
                                    网站首页
                                </a>
                            </div>
                            <div class="link-item">
                                <a href="<?php echo home_url('/news/'); ?>" class="useful-link">
                                    <i class="anticon anticon-file-text"></i>
                                    新闻中心
                                </a>
                            </div>
                            <?php
                            // 获取菜单项目
                            $menu_items = wp_get_nav_menu_items('menu');
                            if ($menu_items) :
                                $count = 0;
                                foreach ($menu_items as $menu_item) :
                                    if ($count >= 4) break; // 只显示前4个
                                    if ($menu_item->menu_item_parent == 0) : // 只显示顶级菜单
                            ?>
                            <div class="link-item">
                                <a href="<?php echo $menu_item->url; ?>" class="useful-link">
                                    <i class="anticon anticon-right"></i>
                                    <?php echo $menu_item->title; ?>
                                </a>
                            </div>
                            <?php
                                    $count++;
                                    endif;
                                endforeach;
                            endif;
                            ?>
                            <div class="link-item">
                                <a href="<?php echo home_url('/sitemap.xml'); ?>" class="useful-link">
                                    <i class="anticon anticon-apartment"></i>
                                    网站地图
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript 功能 -->
<script>
// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 数字动画
    const errorNumber = document.querySelector('.error-number');
    if (errorNumber) {
        errorNumber.style.opacity = '0';
        errorNumber.style.transform = 'scale(0.5)';

        setTimeout(() => {
            errorNumber.style.transition = 'all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            errorNumber.style.opacity = '1';
            errorNumber.style.transform = 'scale(1)';
        }, 300);
    }

    // 图标动画
    const illustration = document.querySelector('.error-illustration');
    if (illustration) {
        illustration.style.opacity = '0';
        illustration.style.transform = 'translateY(20px)';

        setTimeout(() => {
            illustration.style.transition = 'all 0.6s ease-out';
            illustration.style.opacity = '1';
            illustration.style.transform = 'translateY(0)';
        }, 600);
    }

    // 按钮动画
    const buttons = document.querySelectorAll('.error-actions .ant-btn');
    buttons.forEach((button, index) => {
        button.style.opacity = '0';
        button.style.transform = 'translateY(20px)';

        setTimeout(() => {
            button.style.transition = 'all 0.4s ease-out';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, 900 + (index * 100));
    });
});

// 搜索表单增强
document.querySelector('.search-form').addEventListener('submit', function(e) {
    const searchInput = this.querySelector('input[name="s"]');
    if (!searchInput.value.trim()) {
        e.preventDefault();
        searchInput.focus();
        searchInput.style.borderColor = '#ff4d4f';

        setTimeout(() => {
            searchInput.style.borderColor = '';
        }, 2000);
    }
});

// 记录 404 错误（用于统计）
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_view', {
        'page_title': '404 Error',
        'page_location': window.location.href
    });
}
</script>

<?php
get_footer();