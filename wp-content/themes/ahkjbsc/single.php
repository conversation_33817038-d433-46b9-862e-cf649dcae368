<?php
/**
 * The main template file.
 *
 * To override home page (for listing latest post) add home.php into the theme.<br>
 * If front page displays is set to static, the index.php file will be use.<br>
 * If front-page.php exists, it will be override any home page file such as home.php, index.php.<br>
 * To learn more please go to https://developer.wordpress.org/themes/basics/template-hierarchy/ .
 *
 * @package ahkjbsc-old
 */


// begins template. -------------------------------------------------------------------------
get_header();
get_sidebar();
?>
    <div class="page-home-newsDetails container page-container">
        <div class="page-newsDetails-head">
            <div class="page-newsDetails-title"><h2>【安徽新闻联播】截至9月底 安徽实现吸纳技术合同交易成交额864亿元</h2>
            </div>
            <div class="page-newsDetails-time">
                <div>
                    <span>发布时间:</span>
                    <span>2022-10-28</span>
                    <span>发布人:</span>
                    <span>安徽创新馆</span>
                </div>
            </div>
        </div>
        <div class="page-newsDetails-body">
            <div class="component-m-article">

            </div>
        </div>
    </div>

<?php
get_sidebar('right');
get_footer();