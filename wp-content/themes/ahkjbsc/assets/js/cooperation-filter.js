/**
 * Cooperation Archive Dynamic Filtering
 * 
 * @package ahkjbsc
 */

(function($) {
    'use strict';

    // 初始化筛选功能
    function initCooperationFilters() {
        // 筛选按钮点击事件
        $('.filter-btn:not(.more-btn)').on('click', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const filterType = $this.data('filter');
            const filterValue = $this.data('value');
            
            // 更新按钮状态
            $this.siblings('.filter-btn:not(.more-btn)').removeClass('active');
            $this.addClass('active');
            
            // 执行筛选
            applyFilters();
        });
        
        // 更多按钮功能
        $('.more-btn').on('click', function(e) {
            e.preventDefault();
            
            const toggleTarget = $(this).data('toggle');
            const $extendedOptions = $('#' + toggleTarget + '-extended');
            
            if ($extendedOptions.length) {
                if ($extendedOptions.is(':hidden')) {
                    $extendedOptions.slideDown(300);
                    $(this).html('收起 ▲');
                } else {
                    $extendedOptions.slideUp(300);
                    $(this).html('更多 ▼');
                }
            }
        });
        
        // 搜索框回车事件
        $('#cooperation-search').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                applyFilters();
            }
        });
        
        // 排序选择事件
        $('#cooperation-sort').on('change', function() {
            applyFilters();
        });
    }
    
    // 应用筛选
    function applyFilters() {
        const filters = collectFilters();
        
        // 显示加载状态
        showLoading();
        
        // 发送AJAX请求
        $.ajax({
            url: cooperation_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'filter_cooperations',
                nonce: cooperation_ajax.nonce,
                filters: filters,
                paged: 1
            },
            success: function(response) {
                if (response.success) {
                    updateCooperationList(response.data);
                    updateResultsCount(response.data.total);
                    updateURL(filters);
                } else {
                    showError('筛选失败，请重试');
                }
            },
            error: function() {
                showError('网络错误，请重试');
            },
            complete: function() {
                hideLoading();
            }
        });
    }
    
    // 收集筛选条件
    function collectFilters() {
        const filters = {};
        
        // 收集所有激活的筛选条件
        $('.filter-btn.active').each(function() {
            const filterType = $(this).data('filter');
            const filterValue = $(this).data('value');
            
            if (filterValue && filterType) {
                filters[filterType] = filterValue;
            }
        });
        
        // 添加搜索关键词
        const searchValue = $('#cooperation-search').val().trim();
        if (searchValue) {
            filters.search = searchValue;
        }
        
        // 添加排序
        const sortValue = $('#cooperation-sort').val();
        if (sortValue) {
            filters.sort = sortValue;
        }
        
        return filters;
    }
    
    // 更新机构列表
    function updateCooperationList(data) {
        const $cooperationList = $('#cooperation-list .cooperation-grid');
        
        if (data.html) {
            $cooperationList.fadeOut(200, function() {
                $(this).html(data.html).fadeIn(200);
                
                // 重新初始化卡片动画
                initCardAnimations();
            });
        } else {
            $cooperationList.fadeOut(200, function() {
                $(this).html('<div class="no-results"><div class="ant-empty"><p class="ant-empty-description">暂无符合条件的合作机构</p></div></div>').fadeIn(200);
            });
        }
        
        // 更新分页
        if (data.pagination) {
            $('.cooperation-pagination').html(data.pagination);
        } else {
            $('.cooperation-pagination').empty();
        }
    }
    
    // 更新结果数量
    function updateResultsCount(total) {
        $('#results-total').html('共找到 <strong>' + total + '</strong> 家合作机构');
    }
    
    // 更新URL
    function updateURL(filters) {
        const urlParams = new URLSearchParams();
        
        Object.keys(filters).forEach(key => {
            if (filters[key]) {
                urlParams.set(key, filters[key]);
            }
        });
        
        const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
        
        // 使用 pushState 更新URL而不刷新页面
        if (window.history && window.history.pushState) {
            window.history.pushState({}, '', newUrl);
        }
    }
    
    // 显示加载状态
    function showLoading() {
        $('#cooperation-list').addClass('loading');
        $('.cooperation-grid').css('opacity', '0.6');
    }
    
    // 隐藏加载状态
    function hideLoading() {
        $('#cooperation-list').removeClass('loading');
        $('.cooperation-grid').css('opacity', '1');
    }
    
    // 显示错误信息
    function showError(message) {
        // 创建错误提示
        const $error = $('<div class="cooperation-error">' + message + '</div>');
        $error.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#ff4d4f',
            color: '#fff',
            padding: '12px 20px',
            borderRadius: '6px',
            zIndex: 9999,
            boxShadow: '0 4px 12px rgba(255, 77, 79, 0.3)'
        });
        
        $('body').append($error);
        
        // 3秒后自动移除
        setTimeout(function() {
            $error.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
    
    // 初始化卡片动画
    function initCardAnimations() {
        $('.cooperation-card').each(function(index) {
            $(this).css({
                opacity: '0',
                transform: 'translateY(20px)'
            }).delay(index * 50).animate({
                opacity: 1
            }, 300).css('transform', 'translateY(0)');
        });
    }
    
    // 收藏功能
    function initFavoriteFunction() {
        $(document).on('click', '.action-btn[title="收藏"]', function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const $icon = $btn.find('i');
            const postId = $btn.closest('.cooperation-card').data('post-id');
            
            // 切换收藏状态
            if ($btn.hasClass('favorited')) {
                $btn.removeClass('favorited');
                $icon.removeClass('favorited');
                $btn.css('color', '');
            } else {
                $btn.addClass('favorited');
                $icon.addClass('favorited');
                $btn.css('color', '#ff4d4f');
            }
            
            // 这里可以添加AJAX请求保存收藏状态到数据库
            // saveFavoriteStatus(postId, $btn.hasClass('favorited'));
        });
    }
    
    // 分享功能
    function initShareFunction() {
        $(document).on('click', '.action-btn[title="分享"]', function(e) {
            e.preventDefault();
            
            const $card = $(this).closest('.cooperation-card');
            const title = $card.find('.cooperation-name a').text();
            const url = $card.find('.cooperation-name a').attr('href');
            
            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url).then(function() {
                        showSuccess('链接已复制到剪贴板');
                    });
                } else {
                    // 备用方法
                    const textArea = document.createElement('textarea');
                    textArea.value = url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showSuccess('链接已复制到剪贴板');
                }
            }
        });
    }
    
    // 显示成功信息
    function showSuccess(message) {
        const $success = $('<div class="cooperation-success">' + message + '</div>');
        $success.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#52c41a',
            color: '#fff',
            padding: '12px 20px',
            borderRadius: '6px',
            zIndex: 9999,
            boxShadow: '0 4px 12px rgba(82, 196, 26, 0.3)'
        });
        
        $('body').append($success);
        
        setTimeout(function() {
            $success.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }
    
    // 清除筛选条件
    window.clearFilters = function() {
        $('.filter-btn').removeClass('active');
        $('.filter-btn[data-value=""]').addClass('active');
        $('#cooperation-search').val('');
        $('#cooperation-sort').val('date_desc');
        applyFilters();
    };
    
    // 搜索功能
    window.searchCooperation = function() {
        applyFilters();
    };
    
    // 排序功能
    window.sortCooperation = function() {
        applyFilters();
    };
    
    // 文档就绪时初始化
    $(document).ready(function() {
        initCooperationFilters();
        initFavoriteFunction();
        initShareFunction();
        initCardAnimations();
    });
    
})(jQuery);
