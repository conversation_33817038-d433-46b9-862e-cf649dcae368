(function ($) {
    $('.ant-menu-submenu').hover(function () {
        let $this = $(this);
        console.log('$this', $this)
        let $popup = $this.children('.ant-menu-submenu-popup')
        setTimeout(function () {
            $this.addClass('ant-menu-submenu-open ant-menu-submenu-active')
            if ($popup.length) {
                $popup.show()
                $($popup[0]).find('.ant-menu-submenu-content')
                    .addClass('slide-up-enter slide-up-enter-active').delay(200)
                    .queue(function (next) {
                        $(this).removeClass('slide-up-enter slide-up-enter-active').show();
                        next();
                    });
            }

        }, 200)

    }, function () {
        let $this = $(this);
        setTimeout(function () {
            let $popup = $this.children('.ant-menu-submenu-popup')
            console.log('$popup', $popup)
            $this.removeClass('ant-menu-submenu-open ant-menu-submenu-active')
            if ($popup.length) {
                $($popup[0]).children('.ant-menu-submenu-content')
                    .addClass('slide-up-leave slide-up-leave-active').delay(200)
                    .queue(function (next) {
                        $(this).removeClass('slide-up-leave slide-up-leave-active');
                        next();
                    });
                $($popup[0]).addClass('slide-up-leave slide-up-leave-active').delay(200).queue(function (next) {
                    $(this).removeClass('slide-up-leave slide-up-leave-active').hide();
                    next();
                })
            }


        }, 200)
    });
    $('.ant-cascader-picker').click(function () {
        let $this = $(this);
        console.log('$this', $this.offset())
        let isOpen = $this.hasClass('ant-cascader-picker-focused')
        $this.toggleClass('ant-cascader-picker-focused')
        $this.children('.ant-cascader-picker-arrow').toggleClass('ant-cascader-picker-arrow-expand')
        if (isOpen) {
            $('.ant-cascader-menus').addClass('slide-up-leave slide-up-leave-active').delay(200).queue(function (next) {
                $(this).removeClass('slide-up-leave slide-up-leave-active').hide();
                next();
            })
        } else {
            $('.ant-cascader-menus').css('left', $this.offset().left + 'px').show().addClass('slide-up-enter slide-up-enter-active').delay(200)
                .queue(function (next) {
                    $(this).removeClass('slide-up-enter slide-up-enter-active');
                    next();
                })
            $('.ant-cascader-menu:last-child').hide()
        }

    });
    $('.ant-cascader-picker').blur(function () {
        let $this = $(this);
        $this.removeClass('ant-cascader-picker-focused')
        $this.children('.ant-cascader-picker-arrow').removeClass('ant-cascader-picker-arrow-expand')
        $('.ant-cascader-menus').addClass('slide-up-leave slide-up-leave-active').delay(200).queue(function (next) {
            $(this).removeClass('slide-up-leave slide-up-leave-active').hide();
            $('.ant-cascader-menu:last-child').hide()
            next();
        })
    });
    $('.ant-cascader-menu-item-expand').hover(function () {
        $(this).parent().next().show()
    });

    // Handle click on cascader menu items
    $('.ant-cascader-menu-item').click(function () {
        const $this = $(this);
        const value = $this.data('value');
        const link = $this.data('link');

        // Update the cascader picker label
        $('.ant-cascader-picker-label').text($this.text());

        // Close the cascader menu
        $('.ant-cascader-menus').hide();
        $('.ant-cascader-picker').removeClass('ant-cascader-picker-focused');
        $('.ant-cascader-picker-arrow').removeClass('ant-cascader-picker-arrow-expand');

        // If there's a link URL, navigate to it
        if (link && link.trim() !== '') {
            window.location.href = link;
        }
    });
    jQuery(".slideBox").slide({mainCell: ".bd ul", effect: "left", autoPlay: false});
    jQuery(".page-home .ant-tabs").slide({
        titCell: ".ant-tabs-nav-list .ant-tabs-tab",
        mainCell: ".ant-tabs-content",
        titOnClassName: "ant-tabs-tab-active",
        trigger: "click",
        autoPlay: false,
        effect: "left",
        startFun: function (i, c, slider, titCell, mainCell, targetCell, prevCell, nextCell) {
            console.log(i, c, slider, titCell)
            const title = titCell[i]
            console.log(title)
            if (title){
                console.log(`$(title).offset()`, $(title).offset(), $(title).width());
                $('.ant-tabs-ink-bar').css('left', $(title).offset().left - $(titCell[0]).offset().left - 8 + 'px').css('width', title.offsetWidth + 8 + 'px')
            }
        }
    });
    jQuery(".page-home .ant-tabs-content .ant-tabs-tabpane").slide({
        mainCell: "ul",
        autoPage: true,
        effect: "top",
        autoPlay: true,
        vis: 5
    });
})(jQuery)