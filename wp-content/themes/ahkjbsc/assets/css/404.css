/* 404 Page Styles */

.page-404 {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 404 主内容区域 */
.error-404-wrapper {
    padding: 80px 0 60px 0;
    text-align: center;
}

.error-404-content {
    max-width: 800px;
    margin: 0 auto;
}

.error-404-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 60px;
    margin-bottom: 40px;
}

/* 404 图标和数字 */
.error-404-image {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.error-number {
    font-size: 120px;
    font-weight: 900;
    color: #1890ff;
    text-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
    line-height: 1;
    font-family: 'Arial Black', Arial, sans-serif;
}

.error-illustration {
    width: 200px;
    height: auto;
}

.error-illustration svg {
    width: 100%;
    height: auto;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

/* 404 文字内容 */
.error-404-text {
    text-align: left;
    max-width: 400px;
}

.error-title {
    font-size: 36px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 20px 0;
    line-height: 1.2;
}

.error-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.65);
    margin: 0 0 32px 0;
}

/* 操作按钮 */
.error-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
}

.error-actions .ant-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 160px;
    justify-content: center;
    height: 44px;
    font-size: 14px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-actions .ant-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-btn-lg {
    height: 44px;
    padding: 8px 24px;
    font-size: 16px;
}

.ant-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border: none;
    color: #fff;
}

.ant-btn-primary:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.ant-btn-default {
    background: #fff;
    border: 1px solid #d9d9d9;
    color: rgba(0, 0, 0, 0.65);
}

.ant-btn-default:hover {
    border-color: #1890ff;
    color: #1890ff;
}

/* 帮助内容区域 */
.error-404-help {
    background: #fff;
    padding: 60px 0;
    border-top: 1px solid #f0f0f0;
}

.page-404 .help-section {
    background: #fff;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    height: 100%;
}

.page-404 .search-section {
    text-align: center;
    margin-bottom: 40px;
}

.page-404 .help-title {
    font-size: 24px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-404 .help-title .anticon {
    color: #1890ff;
    font-size: 28px;
}

.help-section-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
}

.help-section-title .anticon {
    color: #1890ff;
    font-size: 16px;
}

/* 搜索表单 */
.search-form-wrapper {
    max-width: 500px;
    margin: 0 auto;
}

.search-form {
    width: 100%;
}

.ant-input-search-large .ant-input {
    height: 48px;
    font-size: 16px;
    border-radius: 8px 0 0 8px;
    border: 2px solid #d9d9d9;
    transition: all 0.3s;
}

.ant-input-search-large .ant-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ant-input-search-large .ant-input-group-addon {
    border: 2px solid #d9d9d9;
    border-left: none;
    border-radius: 0 8px 8px 0;
    background: transparent;
    padding: 0;
}

.ant-input-search-large .ant-btn {
    height: 44px;
    width: 48px;
    border: none;
    border-radius: 0 6px 6px 0;
    background: #1890ff;
    color: #fff;
}

.ant-input-search-large .ant-btn:hover {
    background: #40a9ff;
}

.ant-btn-icon-only {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 分类列表 */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.category-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.category-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s;
}

.category-link:hover {
    background: #f0f8ff;
    color: #1890ff;
    transform: translateX(4px);
}

.category-name {
    font-weight: 500;
}

.category-count {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

/* 最新文章列表 */
.recent-posts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recent-post-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.recent-post-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.recent-post-link {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s;
    gap: 12px;
}

.recent-post-link:hover {
    background: #f0f8ff;
    color: #1890ff;
    transform: translateX(4px);
}

.recent-post-title {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.recent-post-date {
    flex-shrink: 0;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    white-space: nowrap;
}

/* 常用链接 */
.useful-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.link-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.link-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.useful-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s;
}

.useful-link:hover {
    background: #f0f8ff;
    color: #1890ff;
    transform: translateX(4px);
}

.useful-link .anticon {
    color: #1890ff;
    font-size: 14px;
}

/* 联系信息区域 */
.error-404-contact {
    background: #fafafa;
    padding: 40px 0;
    border-top: 1px solid #f0f0f0;
    text-align: center;
}

.contact-section {
    max-width: 600px;
    margin: 0 auto;
}

.contact-title {
    font-size: 24px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 16px 0;
}

.contact-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.65);
    margin: 0 0 24px 0;
}

.contact-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.contact-actions .ant-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
    padding: 8px 20px;
    border-radius: 6px;
    transition: all 0.3s;
}

/* 空状态 */
.no-categories,
.no-posts {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-style: italic;
    padding: 20px;
    background: #fafafa;
    border-radius: 6px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-404-wrapper {
        padding: 40px 0;
    }
    
    .error-404-main {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }
    
    .error-404-text {
        text-align: center;
        max-width: none;
    }
    
    .error-number {
        font-size: 80px;
    }
    
    .error-title {
        font-size: 28px;
    }
    
    .error-actions {
        align-items: center;
        width: 100%;
    }
    
    .error-actions .ant-btn {
        width: 100%;
        max-width: 280px;
    }
    
    .error-illustration {
        width: 150px;
    }
    
    .help-section {
        padding: 24px 20px;
        margin: 0 16px 20px 16px;
    }
    
    .help-title {
        font-size: 20px;
    }
    
    .help-section-title {
        font-size: 16px;
    }
    
    .ant-input-search-large .ant-input {
        height: 44px;
        font-size: 14px;
    }
    
    .ant-input-search-large .ant-btn {
        height: 40px;
        width: 44px;
    }
    
    .contact-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-actions .ant-btn {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .error-404-wrapper {
        padding: 20px 0;
    }
    
    .error-number {
        font-size: 60px;
    }
    
    .error-title {
        font-size: 24px;
    }
    
    .error-description {
        font-size: 14px;
    }
    
    .error-illustration {
        width: 120px;
    }
    
    .help-section {
        padding: 20px 16px;
        margin: 0 12px 16px 12px;
    }
    
    .help-title {
        font-size: 18px;
    }
    
    .help-section-title {
        font-size: 14px;
    }
    
    .recent-post-link,
    .category-link,
    .useful-link {
        padding: 6px 8px;
    }
    
    .recent-post-title {
        font-size: 13px;
    }
    
    .contact-title {
        font-size: 20px;
    }
    
    .contact-description {
        font-size: 14px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 加载动画类 */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* 悬停效果增强 */
.help-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.category-link:hover .category-count,
.recent-post-link:hover .recent-post-date {
    color: #1890ff;
}

/* 焦点样式 */
.ant-input:focus,
.ant-btn:focus {
    outline: none;
}

.ant-btn:focus-visible {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
