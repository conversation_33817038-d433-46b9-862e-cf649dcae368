body, html {
    width: 100%;
    height: 100%
}

input::-ms-clear, input::-ms-reveal {
    display: none
}

*, :after, :before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

@-ms-viewport {
    width: device-width
}

article, aside, dialog, figcaption, figure, footer, header, hgroup, main, nav, section {
    display: block
}

body {
    margin: 0;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-variant: tabular-nums;
    line-height: 1.5;
    background-color: #fff;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum"
}

[tabindex="-1"]:focus {
    outline: none !important
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: .5em;
    color: rgba(0, 0, 0, .85);
    font-weight: 500
}

p {
    margin-top: 0;
    margin-bottom: 1em
}

abbr[data-original-title], abbr[title] {
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    border-bottom: 0;
    cursor: help
}

address {
    margin-bottom: 1em;
    font-style: normal;
    line-height: inherit
}

input[type=number], input[type=password], input[type=text], textarea {
    -webkit-appearance: none
}

dl, ol, ul {
    margin-top: 0;
    margin-bottom: 1em
}

ol ol, ol ul, ul ol, ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 500
}

dd {
    margin-bottom: .5em;
    margin-left: 0
}

blockquote {
    margin: 0 0 1em
}

dfn {
    font-style: italic
}

b, strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

sub, sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

a {
    color: #3a69df;
    text-decoration: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    -webkit-transition: color .3s;
    transition: color .3s;
    -webkit-text-decoration-skip: objects
}

a:hover {
    color: #638eeb
}

a:active {
    color: #274bb8
}

a:active, a:hover {
    text-decoration: none;
    outline: 0
}

a[disabled] {
    color: rgba(0, 0, 0, .25);
    cursor: not-allowed;
    pointer-events: none
}

code, kbd, pre, samp {
    font-size: 1em;
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, Courier, monospace
}

pre {
    margin-top: 0;
    margin-bottom: 1em;
    overflow: auto
}

figure {
    margin: 0 0 1em
}

img {
    vertical-align: middle;
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

[role=button], a, area, button, input:not([type=range]), label, select, summary, textarea {
    -ms-touch-action: manipulation;
    touch-action: manipulation
}

table {
    border-collapse: collapse
}

caption {
    padding-top: .75em;
    padding-bottom: .3em;
    color: rgba(0, 0, 0, .45);
    text-align: left;
    caption-side: bottom
}

th {
    text-align: inherit
}

button, input, optgroup, select, textarea {
    margin: 0;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit
}

button, input {
    overflow: visible
}

button, select {
    text-transform: none
}

[type=reset], [type=submit], button, html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner, [type=reset]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

input[type=checkbox], input[type=radio] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

input[type=date], input[type=datetime-local], input[type=month], input[type=time] {
    -webkit-appearance: listbox
}

textarea {
    overflow: auto;
    resize: vertical
}

fieldset {
    min-width: 0;
    margin: 0;
    padding: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    max-width: 100%;
    margin-bottom: .5em;
    padding: 0;
    color: inherit;
    font-size: 1.5em;
    line-height: inherit;
    white-space: normal
}

progress {
    vertical-align: baseline
}

[type=number]::-webkit-inner-spin-button, [type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: none
}

[type=search]::-webkit-search-cancel-button, [type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

output {
    display: inline-block
}

summary {
    display: list-item
}

template {
    display: none
}

[hidden] {
    display: none !important
}

mark {
    padding: .2em;
    background-color: #feffe6
}

::-moz-selection {
    color: #fff;
    background: #3a69df
}

::selection {
    color: #fff;
    background: #3a69df
}

.clearfix {
    zoom: 1
}

.clearfix:after, .clearfix:before {
    display: table;
    content: ""
}

.clearfix:after {
    clear: both
}


.slide-up-appear, .slide-up-enter, .slide-up-leave {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.slide-up-appear.slide-up-appear-active, .slide-up-enter.slide-up-enter-active {
    -webkit-animation-name: antSlideUpIn;
    animation-name: antSlideUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.slide-up-leave.slide-up-leave-active {
    -webkit-animation-name: antSlideUpOut;
    animation-name: antSlideUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    pointer-events: none
}

.slide-up-appear, .slide-up-enter {
    opacity: 0;
    -webkit-animation-timing-function: cubic-bezier(.23, 1, .32, 1);
    animation-timing-function: cubic-bezier(.23, 1, .32, 1)
}

.slide-up-leave {
    -webkit-animation-timing-function: cubic-bezier(.755, .05, .855, .06);
    animation-timing-function: cubic-bezier(.755, .05, .855, .06)
}

@-webkit-keyframes antSlideUpIn {
    0% {
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 0
    }
    to {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 1
    }
}

@keyframes antSlideUpIn {
    0% {
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 0
    }
    to {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 1
    }
}

@-webkit-keyframes antSlideUpOut {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 1
    }
    to {
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 0
    }
}

@keyframes antSlideUpOut {
    0% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 1
    }
    to {
        -webkit-transform: scaleY(.8);
        transform: scaleY(.8);
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        opacity: 0
    }
}

.ant-layout-footer, .ant-layout-header {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto
}

.ant-layout-header {
    height: 64px;
    padding: 0 50px;
    line-height: 64px;
    background: #001529
}

.ant-layout-footer {
    padding: 24px 50px;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    background: #f0f2f5
}

.ant-layout-content {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    min-height: 0
}

.ant-layout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 0;
    background: #f0f2f5
}

.ant-layout, .ant-layout * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.ant-input {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-variant: tabular-nums;
    list-style: none;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    width: 100%;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    -webkit-transition: all .3s;
    transition: all .3s
}

.ant-input::-moz-placeholder {
    color: #bfbfbf;
    opacity: 1
}

.ant-input:-ms-input-placeholder {
    color: #bfbfbf
}

.ant-input::-webkit-input-placeholder {
    color: #bfbfbf
}

.ant-input:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-input:-ms-input-placeholder {
    text-overflow: ellipsis
}

.ant-input:placeholder-shown {
    text-overflow: ellipsis
}

.ant-input:focus, .ant-input:hover {
    border-color: #638eeb;
    border-right-width: 1px !important
}

.ant-input:focus {
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(58, 105, 223, .2);
    box-shadow: 0 0 0 2px rgba(58, 105, 223, .2)
}

.ant-input-disabled {
    color: rgba(0, 0, 0, .25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1
}

.ant-input-disabled:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important
}

.ant-input[disabled] {
    color: rgba(0, 0, 0, .25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1
}

.ant-input[disabled]:hover {
    border-color: #d9d9d9;
    border-right-width: 1px !important
}
.ant-input-sm {
    height: 24px;
    padding: 1px 7px;
}

.ant-cascader {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum"
}

.ant-cascader-input.ant-input {
    position: static;
    width: 100%;
    padding-right: 24px;
    background-color: transparent !important;
    cursor: pointer
}

.ant-cascader-picker-show-search .ant-cascader-input.ant-input {
    position: relative
}

.ant-cascader-picker {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
    position: relative;
    display: inline-block;
    background-color: #fff;
    border-radius: 4px;
    outline: 0;
    cursor: pointer;
    -webkit-transition: color .3s;
    transition: color .3s
}

.ant-cascader-picker-with-value .ant-cascader-picker-label {
    color: transparent
}

.ant-cascader-picker-disabled {
    color: rgba(0, 0, 0, .25);
    background: #f5f5f5;
    cursor: not-allowed
}

.ant-cascader-picker-disabled .ant-cascader-input {
    cursor: not-allowed
}

.ant-cascader-picker:focus .ant-cascader-input {
    border-color: #638eeb;
    border-right-width: 1px !important;
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(58, 105, 223, .2);
    box-shadow: 0 0 0 2px rgba(58, 105, 223, .2)
}

.ant-cascader-picker-show-search.ant-cascader-picker-focused {
    color: rgba(0, 0, 0, .25)
}

.ant-cascader-picker-label {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 20px;
    margin-top: -10px;
    padding: 0 20px 0 12px;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ant-cascader-picker-clear {
    position: absolute;
    top: 50%;
    right: 12px;
    z-index: 2;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: rgba(0, 0, 0, .25);
    font-size: 12px;
    line-height: 12px;
    background: #fff;
    cursor: pointer;
    opacity: 0;
    -webkit-transition: color .3s ease, opacity .15s ease;
    transition: color .3s ease, opacity .15s ease
}

.ant-cascader-picker-clear:hover {
    color: rgba(0, 0, 0, .45)
}

.ant-cascader-picker:hover .ant-cascader-picker-clear {
    opacity: 1
}

.ant-cascader-picker-arrow {
    position: absolute;
    top: 50%;
    right: 12px;
    z-index: 1;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: rgba(0, 0, 0, .25);
    font-size: 12px;
    line-height: 12px;
    -webkit-transition: -webkit-transform .2s;
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s
}

.ant-cascader-picker-arrow.ant-cascader-picker-arrow-expand {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.ant-cascader-picker-label:hover + .ant-cascader-input {
    border-color: #638eeb;
    border-right-width: 1px !important
}

.ant-cascader-picker-small .ant-cascader-picker-arrow, .ant-cascader-picker-small .ant-cascader-picker-clear {
    right: 8px
}


.ant-cascader-menus {
    position: absolute;
    z-index: 1050;
    font-size: 14px;
    white-space: nowrap;
    background: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15)
}

.ant-cascader-menus ol, .ant-cascader-menus ul {
    margin: 0;
    list-style: none
}

.ant-cascader-menus-empty, .ant-cascader-menus-hidden {
    display: none
}

.ant-cascader-menus.slide-up-appear.slide-up-appear-active.ant-cascader-menus-placement-bottomLeft, .ant-cascader-menus.slide-up-enter.slide-up-enter-active.ant-cascader-menus-placement-bottomLeft {
    -webkit-animation-name: antSlideUpIn;
    animation-name: antSlideUpIn
}

.ant-cascader-menus.slide-up-appear.slide-up-appear-active.ant-cascader-menus-placement-topLeft, .ant-cascader-menus.slide-up-enter.slide-up-enter-active.ant-cascader-menus-placement-topLeft {
    -webkit-animation-name: antSlideDownIn;
    animation-name: antSlideDownIn
}

.ant-cascader-menus.slide-up-leave.slide-up-leave-active.ant-cascader-menus-placement-bottomLeft {
    -webkit-animation-name: antSlideUpOut;
    animation-name: antSlideUpOut
}

.ant-cascader-menus.slide-up-leave.slide-up-leave-active.ant-cascader-menus-placement-topLeft {
    -webkit-animation-name: antSlideDownOut;
    animation-name: antSlideDownOut
}

.ant-cascader-menu {
    display: inline-block;
    min-width: 111px;
    height: 180px;
    margin: 0;
    padding: 4px 0;
    overflow: auto;
    vertical-align: top;
    list-style: none;
    border-right: 1px solid #e8e8e8;
    -ms-overflow-style: -ms-autohiding-scrollbar
}

.ant-cascader-menu:first-child {
    border-radius: 4px 0 0 4px
}

.ant-cascader-menu:last-child {
    margin-right: -1px;
    border-right-color: transparent;
    border-radius: 0 4px 4px 0
}

.ant-cascader-menu:only-child {
    border-radius: 4px
}

.ant-cascader-menu-item {
    padding: 5px 12px;
    line-height: 22px;
    white-space: nowrap;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s
}

.ant-cascader-menu-item:hover {
    background: #f0f7ff
}

.ant-cascader-menu-item-disabled {
    color: rgba(0, 0, 0, .25);
    cursor: not-allowed
}

.ant-cascader-menu-item-disabled:hover {
    background: transparent
}

.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    font-weight: 600;
    background-color: #fafafa
}

.ant-cascader-menu-item-expand {
    position: relative;
    padding-right: 24px
}

.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon, .ant-cascader-menu-item-loading-icon {
    display: inline-block;
    font-size: 12px;
    font-size: 10px \9;
    -webkit-transform: scale(.83333333) rotate(0deg);
    transform: scale(.83333333) rotate(0deg);
    position: absolute;
    right: 12px;
    color: rgba(0, 0, 0, .45)
}

:root .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon, :root .ant-cascader-menu-item-loading-icon {
    font-size: 12px
}

.ant-cascader-menu-item-disabled.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon, .ant-cascader-menu-item-disabled.ant-cascader-menu-item-loading-icon {
    color: rgba(0, 0, 0, .25)
}

.ant-cascader-menu-item .ant-cascader-menu-item-keyword {
    color: #f5222d
}

.ant-menu {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
    margin-bottom: 0;
    padding-left: 0;
    color: rgba(0, 0, 0, .65);
    line-height: 0;
    list-style: none;
    background: #fff;
    outline: none;
    -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    -webkit-transition: background .3s, width .3s cubic-bezier(.2, 0, 0, 1) 0s;
    transition: background .3s, width .3s cubic-bezier(.2, 0, 0, 1) 0s;
    zoom: 1
}

.ant-menu:after, .ant-menu:before {
    display: table;
    content: ""
}

.ant-menu:after {
    clear: both
}

.ant-menu ol, .ant-menu ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.ant-menu-hidden {
    display: none
}

.ant-menu-item-group-title {
    padding: 8px 16px;
    color: rgba(0, 0, 0, .45);
    font-size: 14px;
    line-height: 1.5;
    -webkit-transition: all .3s;
    transition: all .3s
}

.ant-menu-submenu, .ant-menu-submenu-inline {
    -webkit-transition: border-color .3s cubic-bezier(.645, .045, .355, 1), background .3s cubic-bezier(.645, .045, .355, 1), padding .15s cubic-bezier(.645, .045, .355, 1);
    transition: border-color .3s cubic-bezier(.645, .045, .355, 1), background .3s cubic-bezier(.645, .045, .355, 1), padding .15s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-submenu-selected {
    color: #3a69df
}

.ant-menu-item:active, .ant-menu-submenu-title:active {
    background: #f0f7ff
}

.ant-menu-submenu .ant-menu-sub {
    cursor: auto;
    -webkit-transition: background .3s cubic-bezier(.645, .045, .355, 1), padding .3s cubic-bezier(.645, .045, .355, 1);
    transition: background .3s cubic-bezier(.645, .045, .355, 1), padding .3s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-item > a {
    display: block;
    color: rgba(0, 0, 0, .65)
}

.ant-menu-item > a:hover {
    color: #3a69df
}

.ant-menu-item > a:before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: transparent;
    content: ""
}

.ant-menu-item > .ant-badge > a {
    color: rgba(0, 0, 0, .65)
}

.ant-menu-item > .ant-badge > a:hover {
    color: #3a69df
}

.ant-menu-item-divider {
    height: 1px;
    overflow: hidden;
    line-height: 0;
    background-color: #e8e8e8
}

.ant-menu-item-active, .ant-menu-item:hover, .ant-menu-submenu-active, .ant-menu-submenu-title:hover, .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open {
    color: #3a69df
}

.ant-menu-horizontal .ant-menu-item, .ant-menu-horizontal .ant-menu-submenu {
    margin-top: -1px
}

.ant-menu-horizontal > .ant-menu-item-active, .ant-menu-horizontal > .ant-menu-item:hover, .ant-menu-horizontal > .ant-menu-submenu .ant-menu-submenu-title:hover {
    background-color: transparent
}

.ant-menu-item-selected, .ant-menu-item-selected > a, .ant-menu-item-selected > a:hover {
    color: #3a69df
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: #f0f7ff
}

.ant-menu-inline, .ant-menu-vertical, .ant-menu-vertical-left {
    border-right: 1px solid #e8e8e8
}

.ant-menu-vertical-right {
    border-left: 1px solid #e8e8e8
}

.ant-menu-vertical-left.ant-menu-sub, .ant-menu-vertical-right.ant-menu-sub, .ant-menu-vertical.ant-menu-sub {
    min-width: 160px;
    padding: 0;
    border-right: 0;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0
}

.ant-menu-vertical-left.ant-menu-sub .ant-menu-item, .ant-menu-vertical-right.ant-menu-sub .ant-menu-item, .ant-menu-vertical.ant-menu-sub .ant-menu-item {
    left: 0;
    margin-left: 0;
    border-right: 0
}

.ant-menu-vertical-left.ant-menu-sub .ant-menu-item:after, .ant-menu-vertical-right.ant-menu-sub .ant-menu-item:after, .ant-menu-vertical.ant-menu-sub .ant-menu-item:after {
    border-right: 0
}

.ant-menu-vertical-left.ant-menu-sub > .ant-menu-item, .ant-menu-vertical-left.ant-menu-sub > .ant-menu-submenu, .ant-menu-vertical-right.ant-menu-sub > .ant-menu-item, .ant-menu-vertical-right.ant-menu-sub > .ant-menu-submenu, .ant-menu-vertical.ant-menu-sub > .ant-menu-item, .ant-menu-vertical.ant-menu-sub > .ant-menu-submenu {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0
}


.ant-menu-horizontal.ant-menu-sub {
    min-width: 114px
}

.ant-menu-item, .ant-menu-submenu-title {
    position: relative;
    display: block;
    margin: 0;
    padding: 0 20px;
    white-space: nowrap;
    cursor: pointer;
    -webkit-transition: color .3s cubic-bezier(.645, .045, .355, 1), border-color .3s cubic-bezier(.645, .045, .355, 1), background .3s cubic-bezier(.645, .045, .355, 1), padding .15s cubic-bezier(.645, .045, .355, 1);
    transition: color .3s cubic-bezier(.645, .045, .355, 1), border-color .3s cubic-bezier(.645, .045, .355, 1), background .3s cubic-bezier(.645, .045, .355, 1), padding .15s cubic-bezier(.645, .045, .355, 1)
}
.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub .ant-menu-item:hover {
    background-color: #082366;
}
.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub .ant-menu-item.ant-menu-item-active {
    background-color: #082366;
}
.ant-menu-item .anticon, .ant-menu-submenu-title .anticon {
    min-width: 14px;
    margin-right: 10px;
    font-size: 14px;
    -webkit-transition: font-size .15s cubic-bezier(.215, .61, .355, 1), margin .3s cubic-bezier(.645, .045, .355, 1);
    transition: font-size .15s cubic-bezier(.215, .61, .355, 1), margin .3s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-item .anticon + span, .ant-menu-submenu-title .anticon + span {
    opacity: 1;
    -webkit-transition: opacity .3s cubic-bezier(.645, .045, .355, 1), width .3s cubic-bezier(.645, .045, .355, 1);
    transition: opacity .3s cubic-bezier(.645, .045, .355, 1), width .3s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu > .ant-menu-item-divider {
    height: 1px;
    margin: 1px 0;
    padding: 0;
    overflow: hidden;
    line-height: 0;
    background-color: #e8e8e8
}

.ant-menu-submenu-popup {
    position: absolute;
    z-index: 1050;
    background: #fff;
    border-radius: 4px
}

.ant-menu-submenu-popup .submenu-title-wrapper {
    padding-right: 20px
}

.ant-menu-submenu-popup:before {
    position: absolute;
    top: -7px;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: .0001;
    content: " "
}

.ant-menu-submenu > .ant-menu {
    background-color: #fff;
    border-radius: 4px
}

.ant-menu-submenu > .ant-menu-submenu-title:after {
    -webkit-transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow, .ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow {
    position: absolute;
    top: 50%;
    right: 16px;
    width: 10px;
    -webkit-transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: transform .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
    position: absolute;
    width: 6px;
    height: 1.5px;
    background: #fff;
    background: rgba(0, 0, 0, .65) \9;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .65)), to(rgba(0, 0, 0, .65)));
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .65), rgba(0, 0, 0, .65));
    background-image: none \9;
    border-radius: 2px;
    -webkit-transition: background .3s cubic-bezier(.645, .045, .355, 1), top .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: background .3s cubic-bezier(.645, .045, .355, 1), top .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    transition: background .3s cubic-bezier(.645, .045, .355, 1), transform .3s cubic-bezier(.645, .045, .355, 1), top .3s cubic-bezier(.645, .045, .355, 1);
    transition: background .3s cubic-bezier(.645, .045, .355, 1), transform .3s cubic-bezier(.645, .045, .355, 1), top .3s cubic-bezier(.645, .045, .355, 1), -webkit-transform .3s cubic-bezier(.645, .045, .355, 1);
    content: ""
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
    -webkit-transform: rotate(45deg) translateY(-2px);
    transform: rotate(45deg) translateY(-2px)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:after {
    -webkit-transform: rotate(-45deg) translateY(2px);
    transform: rotate(-45deg) translateY(2px)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-inline > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-left > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical-right > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before, .ant-menu-submenu-vertical > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after, .ant-menu-submenu-vertical > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before {
    background: -webkit-gradient(linear, left top, right top, from(#3a69df), to(#3a69df));
    background: linear-gradient(90deg, #3a69df, #3a69df)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
    -webkit-transform: rotate(-45deg) translateX(2px);
    transform: rotate(-45deg) translateX(2px)
}

.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:after {
    -webkit-transform: rotate(45deg) translateX(-2px);
    transform: rotate(45deg) translateX(-2px)
}

.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px)
}

.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:after {
    -webkit-transform: rotate(-45deg) translateX(-2px);
    transform: rotate(-45deg) translateX(-2px)
}

.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
    -webkit-transform: rotate(45deg) translateX(2px);
    transform: rotate(45deg) translateX(2px)
}

.ant-menu-vertical-left .ant-menu-submenu-selected, .ant-menu-vertical-left .ant-menu-submenu-selected > a, .ant-menu-vertical-right .ant-menu-submenu-selected, .ant-menu-vertical-right .ant-menu-submenu-selected > a, .ant-menu-vertical .ant-menu-submenu-selected, .ant-menu-vertical .ant-menu-submenu-selected > a {
    color: #3a69df
}

.ant-menu-horizontal {
    line-height: 46px;
    white-space: nowrap;
    border: 0;
    border-bottom: 1px solid #e8e8e8;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ant-menu-horizontal > .ant-menu-item, .ant-menu-horizontal > .ant-menu-submenu {
    position: relative;
    top: 1px;
    display: inline-block;
    vertical-align: bottom;
    border-bottom: 2px solid transparent
}

.ant-menu-horizontal > .ant-menu-item-active, .ant-menu-horizontal > .ant-menu-item-open, .ant-menu-horizontal > .ant-menu-item-selected, .ant-menu-horizontal > .ant-menu-item:hover, .ant-menu-horizontal > .ant-menu-submenu-active, .ant-menu-horizontal > .ant-menu-submenu-open, .ant-menu-horizontal > .ant-menu-submenu-selected, .ant-menu-horizontal > .ant-menu-submenu:hover {
    color: #3a69df;
    border-bottom: 2px solid #3a69df
}

.ant-menu-horizontal > .ant-menu-item > a {
    display: block;
    color: rgba(0, 0, 0, .65)
}

.ant-menu-horizontal > .ant-menu-item > a:hover {
    color: #3a69df
}

.ant-menu-horizontal > .ant-menu-item > a:before {
    bottom: -2px
}

.ant-menu-horizontal > .ant-menu-item-selected > a {
    color: #3a69df
}

.ant-menu-horizontal:after {
    display: block;
    clear: both;
    height: 0;
    content: "\20"
}

.ant-menu-inline .ant-menu-item, .ant-menu-vertical-left .ant-menu-item, .ant-menu-vertical-right .ant-menu-item, .ant-menu-vertical .ant-menu-item {
    position: relative
}

.ant-menu-inline .ant-menu-item:after, .ant-menu-vertical-left .ant-menu-item:after, .ant-menu-vertical-right .ant-menu-item:after, .ant-menu-vertical .ant-menu-item:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border-right: 3px solid #3a69df;
    -webkit-transform: scaleY(.0001);
    transform: scaleY(.0001);
    opacity: 0;
    -webkit-transition: opacity .15s cubic-bezier(.215, .61, .355, 1), -webkit-transform .15s cubic-bezier(.215, .61, .355, 1);
    transition: opacity .15s cubic-bezier(.215, .61, .355, 1), -webkit-transform .15s cubic-bezier(.215, .61, .355, 1);
    transition: transform .15s cubic-bezier(.215, .61, .355, 1), opacity .15s cubic-bezier(.215, .61, .355, 1);
    transition: transform .15s cubic-bezier(.215, .61, .355, 1), opacity .15s cubic-bezier(.215, .61, .355, 1), -webkit-transform .15s cubic-bezier(.215, .61, .355, 1);
    content: ""
}

.ant-menu-inline .ant-menu-item, .ant-menu-inline .ant-menu-submenu-title, .ant-menu-vertical-left .ant-menu-item, .ant-menu-vertical-left .ant-menu-submenu-title, .ant-menu-vertical-right .ant-menu-item, .ant-menu-vertical-right .ant-menu-submenu-title, .ant-menu-vertical .ant-menu-item, .ant-menu-vertical .ant-menu-submenu-title {
    height: 40px;
    margin-top: 4px;
    margin-bottom: 4px;
    padding: 0 16px;
    overflow: hidden;
    font-size: 14px;
    line-height: 40px;
    text-overflow: ellipsis
}

.ant-menu-inline .ant-menu-submenu, .ant-menu-vertical-left .ant-menu-submenu, .ant-menu-vertical-right .ant-menu-submenu, .ant-menu-vertical .ant-menu-submenu {
    padding-bottom: .02px
}

.ant-menu-inline .ant-menu-item:not(:last-child), .ant-menu-vertical-left .ant-menu-item:not(:last-child), .ant-menu-vertical-right .ant-menu-item:not(:last-child), .ant-menu-vertical .ant-menu-item:not(:last-child) {
    margin-bottom: 8px
}

.ant-menu-inline > .ant-menu-item, .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-left > .ant-menu-item, .ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical-right > .ant-menu-item, .ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-vertical > .ant-menu-item, .ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title {
    height: 40px;
    line-height: 40px
}

.ant-menu-inline {
    width: 100%
}

.ant-menu-inline .ant-menu-item-selected:after, .ant-menu-inline .ant-menu-selected:after {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    opacity: 1;
    -webkit-transition: opacity .15s cubic-bezier(.645, .045, .355, 1), -webkit-transform .15s cubic-bezier(.645, .045, .355, 1);
    transition: opacity .15s cubic-bezier(.645, .045, .355, 1), -webkit-transform .15s cubic-bezier(.645, .045, .355, 1);
    transition: transform .15s cubic-bezier(.645, .045, .355, 1), opacity .15s cubic-bezier(.645, .045, .355, 1);
    transition: transform .15s cubic-bezier(.645, .045, .355, 1), opacity .15s cubic-bezier(.645, .045, .355, 1), -webkit-transform .15s cubic-bezier(.645, .045, .355, 1)
}

.ant-menu-inline .ant-menu-item, .ant-menu-inline .ant-menu-submenu-title {
    width: calc(100% + 1px)
}

.ant-menu-inline .ant-menu-submenu-title {
    padding-right: 34px
}

.ant-menu-inline-collapsed {
    width: 80px
}

.ant-menu-inline-collapsed > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
    left: 0;
    padding: 0 32px !important;
    text-overflow: clip
}

.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item .ant-menu-submenu-arrow, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title .ant-menu-submenu-arrow, .ant-menu-inline-collapsed > .ant-menu-item .ant-menu-submenu-arrow, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .ant-menu-submenu-arrow {
    display: none
}

.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item .anticon, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title .anticon, .ant-menu-inline-collapsed > .ant-menu-item .anticon, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .anticon {
    margin: 0;
    font-size: 16px;
    line-height: 40px
}

.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item .anticon + span, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title .anticon + span, .ant-menu-inline-collapsed > .ant-menu-item .anticon + span, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title .anticon + span {
    display: inline-block;
    max-width: 0;
    opacity: 0
}

.ant-menu-inline-collapsed-tooltip {
    pointer-events: none
}

.ant-menu-inline-collapsed-tooltip .anticon {
    display: none
}

.ant-menu-inline-collapsed-tooltip a {
    color: hsla(0, 0%, 100%, .85)
}

.ant-menu-inline-collapsed .ant-menu-item-group-title {
    padding-right: 4px;
    padding-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ant-menu-item-group-list {
    margin: 0;
    padding: 0
}

.ant-menu-item-group-list .ant-menu-item, .ant-menu-item-group-list .ant-menu-submenu-title {
    padding: 0 16px 0 28px
}

.ant-menu-root.ant-menu-inline, .ant-menu-root.ant-menu-vertical, .ant-menu-root.ant-menu-vertical-left, .ant-menu-root.ant-menu-vertical-right, .ant-menu-sub.ant-menu-inline {
    -webkit-box-shadow: none;
    box-shadow: none
}

.ant-menu-sub.ant-menu-inline {
    padding: 0;
    border: 0;
    border-radius: 0
}

.ant-menu-sub.ant-menu-inline > .ant-menu-item, .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
    height: 40px;
    line-height: 40px;
    list-style-position: inside;
    list-style-type: disc
}

.ant-menu-sub.ant-menu-inline .ant-menu-item-group-title {
    padding-left: 32px
}

.ant-menu-item-disabled, .ant-menu-submenu-disabled {
    color: rgba(0, 0, 0, .25) !important;
    background: none;
    border-color: transparent !important;
    cursor: not-allowed
}

.ant-menu-item-disabled > a, .ant-menu-submenu-disabled > a {
    color: rgba(0, 0, 0, .25) !important;
    pointer-events: none
}

.ant-menu-item-disabled > .ant-menu-submenu-title, .ant-menu-submenu-disabled > .ant-menu-submenu-title {
    color: rgba(0, 0, 0, .25) !important;
    cursor: not-allowed
}

.ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before {
    background: rgba(0, 0, 0, .25) !important
}

.ant-menu-dark, .ant-menu-dark .ant-menu-sub {
    color: hsla(0, 0%, 100%, .65);
    background: #001529
}

.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow {
    opacity: .45;
    -webkit-transition: all .3s;
    transition: all .3s
}

.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
    background: #fff
}

.ant-menu-dark.ant-menu-submenu-popup {
    background: transparent
}

.ant-menu-dark .ant-menu-inline.ant-menu-sub {
    background: #000c17;
    -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, .45) inset;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, .45)
}

.ant-menu-dark.ant-menu-horizontal {
    border-bottom: 0
}

.ant-menu-dark.ant-menu-horizontal > .ant-menu-item, .ant-menu-dark.ant-menu-horizontal > .ant-menu-submenu {
    top: 0;
    margin-top: 0;
    border-color: #001529;
    border-bottom: 0
}

.ant-menu-dark.ant-menu-horizontal > .ant-menu-item > a:before {
    bottom: 0
}

.ant-menu-dark .ant-menu-item, .ant-menu-dark .ant-menu-item-group-title, .ant-menu-dark .ant-menu-item > a {
    color: hsla(0, 0%, 100%, .65)
}

.ant-menu-dark.ant-menu-inline, .ant-menu-dark.ant-menu-vertical, .ant-menu-dark.ant-menu-vertical-left, .ant-menu-dark.ant-menu-vertical-right {
    border-right: 0
}

.ant-menu-dark.ant-menu-inline .ant-menu-item, .ant-menu-dark.ant-menu-vertical-left .ant-menu-item, .ant-menu-dark.ant-menu-vertical-right .ant-menu-item, .ant-menu-dark.ant-menu-vertical .ant-menu-item {
    left: 0;
    margin-left: 0;
    border-right: 0
}

.ant-menu-dark.ant-menu-inline .ant-menu-item:after, .ant-menu-dark.ant-menu-vertical-left .ant-menu-item:after, .ant-menu-dark.ant-menu-vertical-right .ant-menu-item:after, .ant-menu-dark.ant-menu-vertical .ant-menu-item:after {
    border-right: 0
}

.ant-menu-dark.ant-menu-inline .ant-menu-item, .ant-menu-dark.ant-menu-inline .ant-menu-submenu-title {
    width: 100%
}

.ant-menu-dark .ant-menu-item-active, .ant-menu-dark .ant-menu-item:hover, .ant-menu-dark .ant-menu-submenu-active, .ant-menu-dark .ant-menu-submenu-open, .ant-menu-dark .ant-menu-submenu-selected, .ant-menu-dark .ant-menu-submenu-title:hover {
    color: #fff;
    background-color: transparent
}

.ant-menu-dark .ant-menu-item-active > a, .ant-menu-dark .ant-menu-item:hover > a, .ant-menu-dark .ant-menu-submenu-active > a, .ant-menu-dark .ant-menu-submenu-open > a, .ant-menu-dark .ant-menu-submenu-selected > a, .ant-menu-dark .ant-menu-submenu-title:hover > a {
    color: #fff
}

.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
    opacity: 1
}

.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after, .ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before {
    background: #fff
}

.ant-menu-submenu.ant-menu-submenu-popup {
    background-color: #123eac;
    width: 100%;
    text-align: center
}

.ant-menu-submenu.ant-menu-submenu-popup.ant-menu-submenu-popup:before {
    background-color: #123eac;
    opacity: 1;
    top: -1px
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub {
    -webkit-transition: none!important;
    transition: none!important;
    color: #fff;
    border-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #123eac;
    -webkit-box-shadow: none;
    box-shadow: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub .ant-menu-item {
    padding: 0 30px
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub .ant-menu-item>a {
    color: #fff
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub .ant-menu-item-active.ant-menu-item-active {
    background-color: #082366
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title {
    padding: 0 30px
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-vertical {
    padding: 0
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-submenu-active .ant-menu-submenu-title,.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-submenu-selected .ant-menu-submenu-title,.ant-menu-submenu.ant-menu-submenu-popup .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: #082366;
    color: #fff
}

.ant-menu-submenu.ant-menu-submenu-popup .ant-menu-submenu-popup {
    width: auto;
    left: 0!important;
    top: 52px!important
}

.ant-menu-submenu .ant-menu-vertical {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ant-menu-submenu .ant-menu-vertical .ant-menu-submenu-vertical {
    position: relative;
    padding: 0 14px
}

.ant-menu-submenu .ant-menu-vertical .ant-menu-submenu-vertical .ant-menu-submenu .ant-menu-vertical {
    display: block
}

.ant-menu-submenu>.ant-menu {
    display: inline-block
}

.ant-menu-submenu>.ant-menu>.ant-menu-vertical-left {
    background-color: #123eac;
    width: auto
}

.ant-menu-submenu>.ant-menu>.ant-menu-vertical-left .ant-menu-vertical.ant-menu-sub {
    min-width: 220px;
    margin-top: -1px;
    color: #fff;
    border-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #123eac;
    -webkit-box-shadow: none;
    box-shadow: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-submenu-arrow {
    right: 3px;
    display: none
}

.ant-divider {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, .65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    -webkit-font-feature-settings: "tnum";
    font-feature-settings: "tnum";
    background: #e8e8e8;
}

.ant-divider, .ant-divider-vertical {
    position: relative;
    top: -.06em;
    display: inline-block;
    width: 1px;
    height: .9em;
    margin: 0 8px;
    vertical-align: middle
}

.ant-row {
    position: relative;
    height: auto;
    margin-right: 0;
    margin-left: 0;
    zoom: 1;
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.ant-row:after, .ant-row:before {
    display: table;
    content: ""
}

.ant-row + .ant-row:before, .ant-row:after {
    clear: both
}

.ant-row-flex {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
}

.ant-row-flex, .ant-row-flex:after, .ant-row-flex:before {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ant-row-flex-start {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.ant-row-flex-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.ant-row-flex-end {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.ant-row-flex-space-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.ant-row-flex-space-around {
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.ant-row-flex-top {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.ant-row-flex-middle {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.ant-row-flex-bottom {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.ant-col {
    position: relative;
    min-height: 1px
}

.ant-col-1, .ant-col-2, .ant-col-3, .ant-col-4, .ant-col-5, .ant-col-6, .ant-col-7, .ant-col-8, .ant-col-9, .ant-col-10, .ant-col-11, .ant-col-12, .ant-col-13, .ant-col-14, .ant-col-15, .ant-col-16, .ant-col-17, .ant-col-18, .ant-col-19, .ant-col-20, .ant-col-21, .ant-col-22, .ant-col-23, .ant-col-24, .ant-col-lg-1, .ant-col-lg-2, .ant-col-lg-3, .ant-col-lg-4, .ant-col-lg-5, .ant-col-lg-6, .ant-col-lg-7, .ant-col-lg-8, .ant-col-lg-9, .ant-col-lg-10, .ant-col-lg-11, .ant-col-lg-12, .ant-col-lg-13, .ant-col-lg-14, .ant-col-lg-15, .ant-col-lg-16, .ant-col-lg-17, .ant-col-lg-18, .ant-col-lg-19, .ant-col-lg-20, .ant-col-lg-21, .ant-col-lg-22, .ant-col-lg-23, .ant-col-lg-24, .ant-col-md-1, .ant-col-md-2, .ant-col-md-3, .ant-col-md-4, .ant-col-md-5, .ant-col-md-6, .ant-col-md-7, .ant-col-md-8, .ant-col-md-9, .ant-col-md-10, .ant-col-md-11, .ant-col-md-12, .ant-col-md-13, .ant-col-md-14, .ant-col-md-15, .ant-col-md-16, .ant-col-md-17, .ant-col-md-18, .ant-col-md-19, .ant-col-md-20, .ant-col-md-21, .ant-col-md-22, .ant-col-md-23, .ant-col-md-24, .ant-col-sm-1, .ant-col-sm-2, .ant-col-sm-3, .ant-col-sm-4, .ant-col-sm-5, .ant-col-sm-6, .ant-col-sm-7, .ant-col-sm-8, .ant-col-sm-9, .ant-col-sm-10, .ant-col-sm-11, .ant-col-sm-12, .ant-col-sm-13, .ant-col-sm-14, .ant-col-sm-15, .ant-col-sm-16, .ant-col-sm-17, .ant-col-sm-18, .ant-col-sm-19, .ant-col-sm-20, .ant-col-sm-21, .ant-col-sm-22, .ant-col-sm-23, .ant-col-sm-24, .ant-col-xs-1, .ant-col-xs-2, .ant-col-xs-3, .ant-col-xs-4, .ant-col-xs-5, .ant-col-xs-6, .ant-col-xs-7, .ant-col-xs-8, .ant-col-xs-9, .ant-col-xs-10, .ant-col-xs-11, .ant-col-xs-12, .ant-col-xs-13, .ant-col-xs-14, .ant-col-xs-15, .ant-col-xs-16, .ant-col-xs-17, .ant-col-xs-18, .ant-col-xs-19, .ant-col-xs-20, .ant-col-xs-21, .ant-col-xs-22, .ant-col-xs-23, .ant-col-xs-24 {
    position: relative;
    padding-right: 0;
    padding-left: 0
}

.ant-col-1, .ant-col-2, .ant-col-3, .ant-col-4, .ant-col-5, .ant-col-6, .ant-col-7, .ant-col-8, .ant-col-9, .ant-col-10, .ant-col-11, .ant-col-12, .ant-col-13, .ant-col-14, .ant-col-15, .ant-col-16, .ant-col-17, .ant-col-18, .ant-col-19, .ant-col-20, .ant-col-21, .ant-col-22, .ant-col-23, .ant-col-24 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    float: left
}

.ant-col-24 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%
}

.ant-col-23 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 95.83333333%
}

.ant-col-22 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 91.66666667%
}

.ant-col-21 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 87.5%
}

.ant-col-20 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 83.33333333%
}

.ant-col-19 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 79.16666667%
}

.ant-col-18 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 75%
}

.ant-col-17 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 70.83333333%
}

.ant-col-16 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 66.66666667%
}

.ant-col-15 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 62.5%
}

.ant-col-14 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 58.33333333%
}

.ant-col-13 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 54.16666667%
}

.ant-col-12 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%
}

.ant-col-11 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 45.83333333%
}

.ant-col-10 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 41.66666667%
}

.ant-col-9 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 37.5%
}

.ant-col-8 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 33.33333333%
}

.ant-col-7 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 29.16666667%
}

.ant-col-6 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 25%
}

.ant-col-5 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 20.83333333%
}

.ant-col-4 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 16.66666667%
}

.ant-col-3 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 12.5%
}

.ant-col-2 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 8.33333333%
}

.ant-col-1 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 4.16666667%
}
.ant-tabs {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0,0,0,.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    display: flex;
    overflow: hidden
}

.ant-tabs>.ant-tabs-nav,.ant-tabs>div>.ant-tabs-nav {
    position: relative;
    display: flex;
    flex: none;
    align-items: center
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
    position: relative;
    display: inline-block;
    display: flex;
    flex: auto;
    align-self: stretch;
    overflow: hidden;
    white-space: nowrap;
    transform: translate(0)
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:after,.ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap:before,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:after,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap:before {
    position: absolute;
    z-index: 1;
    opacity: 0;
    transition: opacity .3s;
    content: "";
    pointer-events: none
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
    position: relative;
    display: flex;
    transition: transform .3s
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations {
    display: flex;
    align-self: stretch
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-operations-hidden,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-operations-hidden {
    position: absolute;
    visibility: hidden;
    pointer-events: none
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-more,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more {
    position: relative;
    padding: 8px 16px;
    background: transparent;
    border: 0
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-more:after,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-more:after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 5px;
    transform: translateY(100%);
    content: ""
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-add,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-add {
    min-width: 40px;
    margin-left: 2px;
    padding: 0 8px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    outline: none;
    cursor: pointer;
    transition: all .3s cubic-bezier(.645,.045,.355,1)
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-add:hover,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-add:hover {
    color: #4d94ff
}

.ant-tabs>.ant-tabs-nav .ant-tabs-nav-add:active,.ant-tabs>.ant-tabs-nav .ant-tabs-nav-add:focus,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-add:active,.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-add:focus {
    color: #1455d9
}

.ant-tabs-extra-content {
    flex: none
}

.ant-tabs-centered>.ant-tabs-nav .ant-tabs-nav-wrap:not([class*=ant-tabs-nav-wrap-ping]),.ant-tabs-centered>div>.ant-tabs-nav .ant-tabs-nav-wrap:not([class*=ant-tabs-nav-wrap-ping]) {
    justify-content: center
}

.ant-tabs-ink-bar {
    position: absolute;
    background: #2374ff;
    pointer-events: none
}

.ant-tabs-tab {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 12px 0;
    font-size: 14px;
    background: transparent;
    border: 0;
    outline: none;
    cursor: pointer
}
.ant-tabs-bottom, .ant-tabs-top {
    flex-direction: column;
}

.ant-tabs-tab-btn:active,.ant-tabs-tab-btn:focus,.ant-tabs-tab-remove:active,.ant-tabs-tab-remove:focus {
    color: #1455d9
}

.ant-tabs-tab-btn {
    outline: none;
    transition: all .3s
}

.ant-tabs-tab:hover {
    color: #4d94ff
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #2374ff;
    text-shadow: 0 0 .25px currentcolor
}

.ant-tabs-tab.ant-tabs-tab-disabled {
    color: rgba(0,0,0,.25);
    cursor: not-allowed
}

.ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:active,.ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:focus,.ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:active,.ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:focus {
    color: rgba(0,0,0,.25)
}

.ant-tabs-tab .ant-tabs-tab-remove .anticon {
    margin: 0
}

.ant-tabs-tab .anticon {
    margin-right: 12px
}

.ant-tabs-tab+.ant-tabs-tab {
    margin: 0 0 0 32px
}

.ant-tabs-content {
    display: flex;
    width: 100%
}

.ant-tabs-content-holder {
    flex: auto;
    min-width: 0;
    min-height: 0
}

.ant-tabs-content-animated {
    transition: margin .3s
}

.ant-tabs-tabpane {
    flex: none;
    width: 100%;
    outline: none
}
.ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar, .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar {
    bottom: 0;
}
.ant-tabs-bottom>.ant-tabs-nav .ant-tabs-ink-bar-animated, .ant-tabs-bottom>div>.ant-tabs-nav .ant-tabs-ink-bar-animated, .ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar-animated, .ant-tabs-top>div>.ant-tabs-nav .ant-tabs-ink-bar-animated {
    transition: width .3s, left .3s, right .3s;
}